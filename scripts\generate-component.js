#!/usr/bin/env node
const fs = require("fs");
const path = require("path");
const utils = require("./utils/component-utils");

// Argumentos desde CLI
// Ejemplo: node scripts/generate-component.js atom Button Wireframe01
const [, , level, componentName, variantName] = process.argv;

// Validar parámetros
utils.validateParams(level, componentName, variantName);

// Obtener rutas
const paths = utils.getPaths(level, componentName, variantName);

// Crear estructura de componente
function createStructure() {
	if (fs.existsSync(paths.basePath)) {
		console.error(
			`⚠️ La variante ${variantName} ya existe en ${componentName} (${level})`
		);
		process.exit(1);
	}

	// Crear directorio base
	fs.mkdirSync(paths.basePath, { recursive: true });

	// Crear estructura de tecnologías (por ahora solo tailwind y sass)
	const activeTechs = ["astro"]; // Puedes cambiar esto según necesites, por ejemplo: ["tailwind", "sass", "webcomponent", "astro", "livewire"]
	utils.createTechStructure(paths.basePath, componentName, activeTechs);

	// Crear README.md vacío
	fs.writeFileSync(path.join(paths.basePath, "README.md"), "", "utf8");

	console.log(
		`✅ ${componentName}/${variantName} creado con éxito en ${utils.levels[level].folder} 🎉`
	);
	console.log(`📦 Tecnologías generadas: ${activeTechs.join(", ")}`);

	updateIndex();
}

// Actualizar índice
function updateIndex() {
	utils.ensureDocsDir(paths.docsDir);

	const indexContent = utils.loadIndex(paths.indexFile);
	const relativePath = `components/${utils.levels[level].folder}/${componentName}/${variantName}`;
	const newVariantEntry = `  - ${variantName} → [${relativePath}](${relativePath})`;

	// Revisar si ya existe la entrada
	if (indexContent.includes(newVariantEntry)) {
		console.log(`ℹ️ Entrada ya existente en el índice, no se duplicó.`);
		return;
	}

	// Parsear el contenido por secciones
	const sections = utils.parseIndexSections(indexContent);
	const sectionTitle = utils.levels[level].title;

	// Agregar nueva entrada a la sección correspondiente
	if (!sections[sectionTitle]) {
		sections[sectionTitle] = {};
	}

	// Agregar el componente y su variante
	if (!sections[sectionTitle][componentName]) {
		sections[sectionTitle][componentName] = [];
	}

	sections[sectionTitle][componentName].push(newVariantEntry);

	// Ordenar alfabéticamente las variantes de cada componente
	sections[sectionTitle][componentName].sort();

	// Guardar índice actualizado
	const newIndexContent = utils.buildIndexContent(sections);
	utils.saveIndex(paths.indexFile, newIndexContent);

	console.log(
		`📖 Índice actualizado en docs/components-index.md (ordenado alfabéticamente)`
	);
}

createStructure();
