{"name": "lbcdev-web-components", "version": "1.0.0", "description": "Biblioteca de componentes web reutilizables", "private": true, "main": "index.js", "workspaces": ["packages/Atoms/*/*/*", "packages/Molecules/*/*/*", "packages/Organisms/*/*/*", "packages/Templates/*/*/*", "packages/Bundles/*/*/*"], "directories": {"doc": "docs"}, "scripts": {"create": "node scripts/generate-component.js", "delete": "node scripts/delete-component.js", "lint:css": "stylelint '**/*.{css,scss}'", "format": "prettier --write '**/*.{js,jsx,ts,tsx,scss,css,md}'"}, "repository": {"type": "git", "url": "git+https://github.com/Luinux81/lbcdev-web-components.git"}, "author": "Luis <PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/Luinux81/lbcdev-web-components/issues"}, "homepage": "https://github.com/Luinux81/lbcdev-web-components#readme"}