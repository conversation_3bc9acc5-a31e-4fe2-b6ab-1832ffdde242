# Button

Componente reutilizable para botones de **llamada a la acción (CTA)**.
Soporta texto personalizable, variantes de estilo, iconos mediante slots y acciones como abrir/cerrar modales, ejecutar funciones personalizadas o actuar como enlaces.

---

## Props

| Prop          | Tipo                             | Requerido | Default     | Descripción                                                         |
| ------------- | -------------------------------- | --------- | ----------- | ------------------------------------------------------------------- |
| `textoBoton`  | `string`                         | ❌        | —           | Texto que se mostrará en el botón.                                  |
| `modalTarget` | `string`                         | ❌        | —           | ID del modal que se abrirá al hacer clic.                           |
| `closeModal`  | `boolean`                        | ❌        | `false`     | Indica si el botón debe cerrar un modal actual.                     |
| `onClick`     | `string`                         | ❌        | —           | Acción personalizada a ejecutar al hacer clic.                      |
| `class`       | `string`                         | ❌        | —           | Clases adicionales para personalizar estilos.                       |
| `variant`     | `"default" \| "ghost" \| "link"` | ❌        | `"default"` | Estilo predefinido del botón.                                       |
| `href`        | `string`                         | ❌        | —           | Si se proporciona, el componente se renderiza como un enlace `<a>`. |
| `target`      | `string`                         | ❌        | —           | Target del enlace (ej. `_blank`).                                   |

---

## Slots

| Slot         | Descripción                                                     |
| ------------ | --------------------------------------------------------------- |
| `iconBefore` | Contenido que se muestra antes del texto (ejemplo: un icono).   |
| `iconAfter`  | Contenido que se muestra después del texto (ejemplo: un icono). |

---

## Ejemplos

### Botón básico (default)

```astro
<Button textoBoton="Enviar" />
```

### Variantes de estilo

**Ghost**

```astro
<Button textoBoton="Cancelar" variant="ghost" />
```

**Link**

```astro
<Button textoBoton="Más información" variant="link" />
```

### Personalización adicional con `class`

```astro
<Button textoBoton="OK" class="px-3 py-1 text-sm rounded-md" />
```

### Como enlace

**Enlace interno**

```astro
<Button textoBoton="Ir a inicio" href="/inicio" />
```

**Enlace externo**

```astro
<Button textoBoton="Visitar OpenAI" href="https://openai.com" target="_blank" variant="link" />
```

---

## Ejemplos con iconos (slots)

**Icono antes del texto**

```astro
<Button textoBoton="Descargar">
  <span slot="iconBefore" class="material-symbols-outlined">download</span>
</Button>
```

**Icono después del texto**

```astro
<Button textoBoton="Siguiente">
  <span slot="iconAfter" class="material-symbols-outlined">arrow_forward</span>
</Button>
```

**Icono antes y después del texto**

```astro
<Button textoBoton="Guardar">
  <span slot="iconBefore" class="material-symbols-outlined">save</span>
  <span slot="iconAfter" class="material-symbols-outlined">check</span>
</Button>
```

**Enlace con icono**

```astro
<Button textoBoton="Ver más" href="/detalles" variant="ghost">
  <span slot="iconAfter" class="material-symbols-outlined">arrow_forward</span>
</Button>
```
