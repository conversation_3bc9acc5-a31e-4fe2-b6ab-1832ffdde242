---
/**
 * Button - Componente reutilizable para botones o enlaces de llamada a la acción
 *
 * Este componente muestra un botón CTA con mensaje personalizable.
 * Diseñado para ser completamente reutilizable en otros proyectos.
 *
 * @param textoBoton - Texto del botón
 * @param modalTarget - ID del modal objetivo (opcional)
 * @param closeModal - Si debe cerrar el modal actual (default: false)
 * @param onClick - Función onClick personalizada (opcional)
 * @param class - Clases adicionales (opcional)
 * @param variant - Estilo predefinido del botón: "default", "ghost", "link" (default: "default")
 * @param href - URL destino. Si está presente, el componente se renderiza como <a>.
 * @param target - Target para el enlace (ej. "_blank") (opcional)
 */

export interface Props {
	textoBoton?: string;
	modalTarget?: string;
	closeModal?: boolean;
	onClick?: string;
	class?: string;
	variant?: "default" | "ghost" | "link";
	href?: string;
	target?: string;
}

const {
	textoBoton = "",
	modalTarget,
	closeModal = false,
	onClick,
	class: className = "",
	variant = "default",
	href,
	target,
} = Astro.props;

// Variante con padding
const paddingVariants: Record<string, string> = {
	link: "px-0 py-0",
};

// Padding por defecto
const defaultPadding = "px-6 py-3";

// Estilos base + variantes
const base =
	(paddingVariants[variant] || defaultPadding) +
	" rounded-xl font-semibold transition-colors inline-flex justify-center items-center gap-2";

const variants: Record<string, string> = {
	default:
		"bg-primary-600 text-white hover:bg-primary-700 shadow-lg hover:shadow-xl",
	ghost: "bg-transparent border border-primary-600 text-primary-600 hover:bg-primary-50",
	link: "bg-transparent text-primary-600 hover:text-primary-800",
};

const variantClass = variants[variant] || variants.default;
---

{
	href ? (
		<a
			href={href}
			target={target}
			class={`${base} ${variantClass} ${className}`}
		>
			<slot name="iconBefore" />
			<span>{textoBoton}</span>
			<slot name="iconAfter" />
		</a>
	) : (
		<button
			class={`${base} ${variantClass} ${className}`}
			data-modal-target={modalTarget}
			data-modal-close={closeModal ? "" : undefined}
			onclick={onClick}
		>
			<slot name="iconBefore" />
			{textoBoton && <span>{textoBoton}</span>}
			<slot name="iconAfter" />
		</button>
	)
}
