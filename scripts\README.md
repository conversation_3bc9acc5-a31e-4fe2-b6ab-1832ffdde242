# 🚀 Ejemplo de uso

## Crear un átomo

```bash
npm run create atom Button Wireframe01
```

Genera:

```bash
components/atoms/Button/Wireframe01/
├── tailwind/Button.jsx
├── sass/Button.scss
├── webcomponent/Button.js
├── astro/Button.astro
├── livewire/Button.blade.php
└── README.md
```

## Eliminar un átomo

```bash
npm run delete atom Button Wireframe01
```

Elimina:

```bash
components/atoms/Button/Wireframe01/
├── tailwind/Button.jsx
├── sass/Button.scss
├── webcomponent/Button.js
├── astro/Button.astro
├── livewire/Button.blade.php
└── README.md
```

Ejemplo de índice:

```markdown
# 📚 Índice de Componentes

## ⚛️ Átomos

### Avatar

-  Circle → [components/atoms/Avatar/Circle](components/atoms/Avatar/Circle)

### Button

-  Outlined → [components/atoms/Button/Outlined](components/atoms/Button/Outlined)
-  Solid → [components/atoms/Button/Solid](components/atoms/Button/Solid)
-  Wireframe01 → [components/atoms/Button/Wireframe01](components/atoms/Button/Wireframe01)

## 🧩 Moléculas

## 🏗️ Organismos

## 📐 Templates
```
