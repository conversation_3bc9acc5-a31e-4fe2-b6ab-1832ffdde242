#!/usr/bin/env node
const fs = require("fs");
const utils = require("./utils/component-utils");

// Argumentos desde CLI
// Ejemplo: node scripts/delete-component.js atom Button Wireframe01
const [, , level, componentName, variantName] = process.argv;

// Validar parámetros
utils.validateParams(level, componentName, variantName);

// Obtener rutas
const paths = utils.getPaths(level, componentName, variantName);

// Eliminar componente
function deleteComponent() {
	if (!fs.existsSync(paths.basePath)) {
		console.error(
			`⚠️ La variante ${variantName} no existe en ${componentName} (${level})`
		);
		console.log(`📁 Ruta esperada: ${paths.basePath}`);
		process.exit(1);
	}

	// Mostrar información sobre las tecnologías que se van a eliminar
	const existingTechs = utils.getExistingTechs(paths.basePath);
	console.log(
		`📦 Tecnologías encontradas: ${
			existingTechs.length > 0 ? existingTechs.join(", ") : "ninguna"
		}`
	);

	// Eliminar la carpeta de la variante
	utils.removeDirectory(paths.basePath);
	console.log(
		`🗑️ Eliminada variante: ${componentName}/${variantName} (${level})`
	);

	// Verificar si quedan otras variantes del componente
	const remainingVariants = utils.getComponentVariants(paths.componentPath);

	// Si no quedan variantes, eliminar toda la carpeta del componente
	if (remainingVariants.length === 0) {
		utils.removeDirectory(paths.componentPath);
		console.log(
			`📂 Eliminado componente completo: ${componentName} (sin variantes restantes)`
		);
	} else {
		console.log(
			`ℹ️ Componente ${componentName} mantiene ${
				remainingVariants.length
			} variante(s): ${remainingVariants.join(", ")}`
		);
	}

	updateIndex(remainingVariants.length === 0);
}

// Actualizar índice eliminando la entrada
function updateIndex(removeEntireComponent = false) {
	if (!fs.existsSync(paths.indexFile)) {
		console.log(`ℹ️ No existe el archivo de índice: ${paths.indexFile}`);
		return;
	}

	const indexContent = utils.loadIndex(paths.indexFile);
	const relativePath = `components/${utils.levels[level].folder}/${componentName}/${variantName}`;
	const variantEntryToRemove = `  - ${variantName} → [${relativePath}](${relativePath})`;

	// Parsear el contenido por secciones
	const sections = utils.parseIndexSections(indexContent);
	const sectionTitle = utils.levels[level].title;

	// Verificar si la sección y el componente existen
	if (!sections[sectionTitle] || !sections[sectionTitle][componentName]) {
		console.log(
			`ℹ️ Componente no encontrado en el índice: ${componentName} (${level})`
		);
		return;
	}

	if (removeEntireComponent) {
		// Eliminar toda la subsección del componente
		delete sections[sectionTitle][componentName];
		console.log(`📖 Eliminada subsección completa: ${componentName}`);
	} else {
		// Solo eliminar la variante específica
		sections[sectionTitle][componentName] = sections[sectionTitle][
			componentName
		].filter((entry) => entry !== variantEntryToRemove);

		// Mantener ordenamiento alfabético de las variantes restantes
		sections[sectionTitle][componentName].sort();

		console.log(`📖 Eliminada variante del índice: ${variantName}`);
	}

	// Guardar índice actualizado
	const newIndexContent = utils.buildIndexContent(sections);
	utils.saveIndex(paths.indexFile, newIndexContent);

	console.log(`📖 Índice actualizado en: docs/components-index.md`);
}

// Confirmación antes de eliminar
function confirmDeletion() {
	const message = `\n🚨 ¿Estás seguro de que quieres eliminar?
   Componente: ${componentName}
   Variante: ${variantName}
   Nivel: ${level}
   Ruta: ${paths.basePath}

¿Continuar? (y/N): `;

	utils.askConfirmation(message, (confirmed) => {
		if (confirmed) {
			deleteComponent();
			console.log(`✅ Eliminación completada 🎉`);
		} else {
			console.log(`❌ Operación cancelada`);
		}
	});
}

// Ejecutar con confirmación
confirmDeletion();
