const fs = require("fs");
const path = require("path");

// Configuración global
const levels = {
	atom: { folder: "atoms", title: "## ⚛️ Átomos" },
	molecule: { folder: "molecules", title: "## 🧩 Moléculas" },
	organism: { folder: "organisms", title: "## 🏗️ Organismos" },
	template: { folder: "templates", title: "## 📐 Templates" },
};

const techs = ["tailwind", "sass", "webcomponent", "astro", "livewire"];

const initialIndexContent = `# 📚 Índice de Componentes

## ⚛️ Átomos

## 🧩 Moléculas

## 🏗️ Organismos

## 📐 Templates
`;

// Mapeo de tecnologías a extensiones de archivo
const techFileMap = {
	tailwind: (componentName) => `${componentName}.jsx`,
	sass: (componentName) => `${componentName}.scss`,
	webcomponent: (componentName) => `${componentName}.js`,
	astro: (componentName) => `${componentName}.astro`,
	livewire: (componentName) => `${componentName}.blade.php`,
};

// Validaciones
function validateLevel(level) {
	if (!levels[level]) {
		console.error(
			"⚠️ Nivel no válido. Usa: atom | molecule | organism | template"
		);
		process.exit(1);
	}
}

function validateParams(level, componentName, variantName) {
	if (!level || !componentName || !variantName) {
		console.error(
			"❌ Uso incorrecto. Parámetros requeridos: <nivel> <Componente> <Variante>"
		);
		console.error("   Niveles: atom | molecule | organism | template");
		process.exit(1);
	}
	validateLevel(level);
}

// Construcción de rutas
function getPaths(level, componentName, variantName) {
	const baseDir = path.join(__dirname, "..", "..");
	const basePath = path.join(
		baseDir,
		"components",
		levels[level].folder,
		componentName,
		variantName
	);
	const componentPath = path.join(
		baseDir,
		"components",
		levels[level].folder,
		componentName
	);
	const docsDir = path.join(baseDir, "docs");
	const indexFile = path.join(docsDir, "components-index.md");

	return {
		baseDir,
		basePath,
		componentPath,
		docsDir,
		indexFile,
	};
}

// Manejo del índice
function parseIndexSections(content) {
	const sections = {};
	const lines = content.split("\n");
	let currentSection = null;
	let currentComponent = null;

	for (const line of lines) {
		if (line.startsWith("## ")) {
			// Nueva sección principal (Átomos, Moléculas, etc.)
			currentSection = line.trim();
			sections[currentSection] = {};
			currentComponent = null;
		} else if (line.startsWith("### ") && currentSection) {
			// Nueva subsección de componente
			currentComponent = line.replace("### ", "").trim();
			sections[currentSection][currentComponent] = [];
		} else if (
			line.startsWith("  - ") &&
			currentSection &&
			currentComponent
		) {
			// Variante del componente actual
			sections[currentSection][currentComponent].push(line);
		}
	}

	return sections;
}

function buildIndexContent(sections) {
	let content = "# 📚 Índice de Componentes\n\n";

	// Orden fijo de las secciones
	const sectionOrder = [
		"## ⚛️ Átomos",
		"## 🧩 Moléculas",
		"## 🏗️ Organismos",
		"## 📐 Templates",
	];

	sectionOrder.forEach((sectionTitle) => {
		content += sectionTitle + "\n\n";

		if (
			sections[sectionTitle] &&
			Object.keys(sections[sectionTitle]).length > 0
		) {
			// Ordenar componentes alfabéticamente
			const sortedComponents = Object.keys(sections[sectionTitle]).sort();

			sortedComponents.forEach((componentName) => {
				content += `### ${componentName}\n`;

				// Agregar todas las variantes del componente (ya ordenadas)
				sections[sectionTitle][componentName].forEach((variant) => {
					content += variant + "\n";
				});

				content += "\n";
			});
		}
	});

	return content.trim() + "\n";
}

function loadIndex(indexFile) {
	if (fs.existsSync(indexFile)) {
		return fs.readFileSync(indexFile, "utf8");
	}
	return initialIndexContent;
}

function saveIndex(indexFile, content) {
	fs.writeFileSync(indexFile, content, "utf8");
}

// Utilidades del sistema de archivos
function ensureDocsDir(docsDir) {
	if (!fs.existsSync(docsDir)) {
		fs.mkdirSync(docsDir, { recursive: true });
	}
}

function removeDirectory(dirPath) {
	if (fs.existsSync(dirPath)) {
		fs.readdirSync(dirPath).forEach((file) => {
			const curPath = path.join(dirPath, file);
			if (fs.lstatSync(curPath).isDirectory()) {
				removeDirectory(curPath);
			} else {
				fs.unlinkSync(curPath);
			}
		});
		fs.rmdirSync(dirPath);
	}
}

// Obtener las tecnologías disponibles en una variante específica
function getExistingTechs(variantPath) {
	if (!fs.existsSync(variantPath)) {
		return [];
	}

	return fs.readdirSync(variantPath).filter((item) => {
		const itemPath = path.join(variantPath, item);
		return fs.lstatSync(itemPath).isDirectory() && techs.includes(item);
	});
}

// Obtener todas las variantes de un componente
function getComponentVariants(componentPath) {
	if (!fs.existsSync(componentPath)) {
		return [];
	}

	return fs.readdirSync(componentPath).filter((item) => {
		return fs.lstatSync(path.join(componentPath, item)).isDirectory();
	});
}

// Crear estructura de archivos para las tecnologías especificadas
function createTechStructure(basePath, componentName, techsToCreate = techs) {
	techsToCreate.forEach((tech) => {
		const techPath = path.join(basePath, tech);
		fs.mkdirSync(techPath, { recursive: true });

		const fileName = techFileMap[tech](componentName);
		fs.writeFileSync(path.join(techPath, fileName), "", "utf8");
	});
}

// Confirmación interactiva
function askConfirmation(message, callback) {
	const readline = require("readline");
	const rl = readline.createInterface({
		input: process.stdin,
		output: process.stdout,
	});

	rl.question(message, (answer) => {
		const confirmed =
			answer.toLowerCase() === "y" || answer.toLowerCase() === "yes";
		rl.close();
		callback(confirmed);
	});
}

module.exports = {
	// Configuración
	levels,
	techs,
	techFileMap,
	initialIndexContent,

	// Validaciones
	validateLevel,
	validateParams,

	// Rutas
	getPaths,

	// Índice
	parseIndexSections,
	buildIndexContent,
	loadIndex,
	saveIndex,

	// Sistema de archivos
	ensureDocsDir,
	removeDirectory,
	getExistingTechs,
	getComponentVariants,
	createTechStructure,

	// Interacción
	askConfirmation,
};
